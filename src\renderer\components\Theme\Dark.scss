.theme-dark {
  .title-bar .window-actions > li {
    color: $--dk-titlebar-actions-color;
    &:hover {
      background-color: $--dk-titlebar-actions-active-background;
    }
    &.win-close-btn:hover {
      background-color: $--dk-titlebar-close-active-background;
    }
  }

  .logo > a {
    color: $--dk-app-logo-color;
  }

  .app-info .app-version span {
    color: $--dk-app-version-color;
  }

  .app-info .engine-info {
    h4 {
      color: $--dk-app-engine-title-color;
    }
    ul {
      color: $--dk-app-engine-info-color;
    }
  }

  .copyright a {
    color: $--dk-app-copyright-color;
  }

  .aside {
    background-color: $--dk-aside-background;
  }

  .subnav {
    background-color: $--dk-subnav-background;
    color: $--dk-subnav-text-color;
  }

  .subnav-inner {
    h3 {
      color: $--dk-subnav-title-color;
    }
    ul li {
      &:hover,
      &.active {
        background-color: $--dk-subnav-active-background;

        i,
        span,
        svg {
          color: $--dk-subnav-active-text-color;
        }
      }
    }
  }

  .form-actions {
    background: $--dk-form-actions-background;
  }

  .main {
    background-color: $--dk-main-background;
  }

  .panel {
    background-color: $--dk-panel-background;
  }

  .panel .panel-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
    h4 {
      color: $--dk-panel-title-color;
    }
  }

  .task-item {
    background-color: $--dk-task-item-backgroud;
    border-color: $--dk-task-item-border-color;
    &:hover {
      border-color: $--dk-task-item-hover-border-color;
    }
  }

  .selected .task-item {
    border-color: $--dk-task-item-hover-border-color;
  }

  .task-name {
    color: $--dk-task-item-text-color;
  }

  .task-actions {
    color: $--dk-task-action-color;
  }

  .task-item-actions {
    background-color: $--dk-task-item-action-background;
    border-color: $--dk-task-item-action-border-color;
    &:hover {
      border-color: $--dk-task-item-action-hover-border-color;
      color: $--dk-task-item-action-hover-color;
      background-color: $--dk-task-item-action-hover-background;
    }
  }

  .mo-speedometer {
    background-color: $--dk-speedometer-background;
    border-color: $--dk-speedometer-border-color;
  }

  .no-task {
    color: $--dk-no-task-color;
  }

  .mo-table-wrapper {
    border-color: $--dk-table-border-color;
  }

  .graphic-box {
    border-color: $--dk-task-detail-box-border;
    background-color: $--dk-graphic-box-background;
  }

  .graphic-atom-s0 {
    fill: $--dk-graphic-atom-color-0;
  }

  .graphic-atom-s1 {
    fill: $--dk-graphic-atom-color-1;
  }

  .graphic-atom-s2 {
    fill: $--dk-graphic-atom-color-2;
  }

  .graphic-atom-s3 {
    fill: $--dk-graphic-atom-color-3;
  }

  .graphic-atom-s4 {
    fill: $--dk-graphic-atom-color-4;
  }

  .form-static-value {
    color: #e7e7e7;
  }

  /* Element UI
  -------------------------- */
  .el-progress-bar__outer {
    background-color: #4a4a4a;
  }

  .el-input__inner,
  .el-textarea__inner {
    background-color: #373737;
    border-color: #5f5f5f;
    color: #eee;
    &:focus {
      outline: none;
      border-color: $--color-primary;
    }
    &::placeholder {
      color: #777;
    }
  }

  .el-input.is-disabled .el-input__inner {
    background-color: #373737;
    border-color: #5f5f5f;
    color: #aaa;
  }

  .el-input-group__append,
  .el-input-group__prepend {
    background-color: #333;
    border-color: #5f5f5f;
    color: #e7e7e7;
  }

  .el-input-number__increase,
  .el-input-number__decrease {
    background-color: #333;
    color: #e7e7e7;
  }

  .el-input-number__decrease {
    border-right-color: #5f5f5f;
  }

  .el-input-number__increase {
    border-left-color: #5f5f5f;
  }

  .el-input-number.is-controls-right .el-input-number__increase {
    border-left-color: #5f5f5f;
    border-bottom-color: #5f5f5f;
  }

  .el-input-number.is-controls-right .el-input-number__decrease {
    border-left-color: #5f5f5f;
  }

  .el-form-item__label,
  .el-checkbox,
  .el-radio {
    color: $--dk-preference-form-text-color;
  }

  .el-switch__core,
  .el-checkbox__inner {
    border-color: #606060;
    background-color: #5c5d5f;
  }

  .el-select .el-input .el-select__caret {
    color: #e7e7e7;
  }

  .el-select-dropdown {
    background-color: #3d3d3d;
    border-color: #606060;
  }

  .el-select-dropdown__item {
    color: #eee;
    &.selected {
      color: $--color-primary;
    }
    &.hover,
    &:hover {
      background-color: $--color-primary;
      color: #fff;
    }
  }

  .el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
    background-color: #3d3d3d;
    color: $--color-primary;
  }

  .el-upload-dragger {
    background-color: #2d2d2d;
    border-color: #606060;
    > i,
    .el-upload__text {
      color: #a2a3a4;
    }
  }

  .el-popper[x-placement^="top"] .popper__arrow {
    border-top-color: #606060;
    &:after {
      border-top-color: #3d3d3d;
    }
  }

  .el-popper[x-placement^="right"] .popper__arrow {
    border-right-color: #606060;
    &:after {
      border-right-color: #3d3d3d;
    }
  }

  .el-popper[x-placement^="bottom"] .popper__arrow {
    border-bottom-color: #606060;
    &:after {
      border-bottom-color: #3d3d3d;
    }
  }

  .el-popper[x-placement^="left"] .popper__arrow {
    border-left-color: #606060;
    &:after {
      border-left-color: #3d3d3d;
    }
  }

  .el-button {
    background-color: #5b5b5b;
    border-color: #606060;
    color: #e6e6e6;
    &:hover,
    &:focus {
      color: $--color-primary-light-4;
      border-color: #606060;
      background-color: #333;
    }
  }

  .el-button--primary {
    color: #eee;
    background-color: $--color-primary;
    border-color: $--color-primary;
    &:hover,
    &:focus {
      background: $--color-primary;
      border-color: $--color-primary;
      color: #fff;
    }
  }

  .el-button--danger.is-plain {
    color: #ff6157;
    background-color: #5b5b5b;
    border-color: #ffc0bc;
  }

  .el-button--danger.is-plain:hover,
  .el-button--danger.is-plain:focus {
    background-color: #ff6157;
    border-color: #ff6157;
    color: #fff;
  }

  /* Message */
  .el-message {
    background-color: #2d2d2d;
    border-color: #606060;
  }

  .el-message__closeBtn {
    color: rgba(255, 255, 255, 0.3);
  }

  .el-message--info .el-message__content {
    color: #a2a3a4;
  }

  .el-message--success {
    background-color: #2d2d2d;
    border-color: #606060;
    .el-message__content {
      color: #67c23a;
    }
  }

  .el-message--warning {
    background-color: #2d2d2d;
    border-color: #606060;
    .el-message__content {
      color: #e6a23c;
    }
  }

  .el-message--error {
    background-color: #2d2d2d;
    border-color: #606060;
    .el-message__content {
      color: #f56c6c;
    }
  }

  /* Dialog */
  .el-dialog {
    background-color: $--dk-dialog-background;
    .el-dialog__body {
      color: $--dk-dialog-text-color;
    }
  }

  .add-task-dialog .el-dialog__footer {
    background-color: $--dk-add-task-dialog-footer-background;
  }

  .torrent-file-list {
    border-color: $--dk-table-border-color;
  }

  .el-table {
    background-color: $--dk-table-background;
    color: $--dk-table-text-color;
    tr {
      background-color: $--dk-table-background;
    }
    th {
      background-color: $--dk-table-th-background;
      color: $--dk-table-text-color;
    }
    th.is-leaf, td {
      background-color: $--dk-table-background;
      color: $--dk-table-text-color;
      border-bottom-color: $--dk-table-border-color;
    }
  }
  .el-table thead th.is-leaf {
    background-color: $--dk-table-th-background;
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td,
  .el-table--enable-row-hover .el-table__body tr.el-table__row--striped:hover > td {
    background-color: $--dk-table-hover-background;
  }

  .el-table--group::after, .el-table--border::after, .el-table::before {
    background-color: $--dk-table-border-color;
  }

  .el-table--striped .el-table__body tr.el-table__row--striped td {
    background-color: $--dk-table-striped-background;
  }

  /* Tabs */
  .el-tabs__item {
    color: #a2a3a4;
    &.is-active {
      color: $--color-primary;
    }
  }

  .el-tabs__nav-wrap::after {
    background-color: #606060;
  }

  .form-preference .el-form-item__content {
    color: $--dk-preference-form-text-color;
  }

  .form-preference .el-switch__label {
    color: $--dk-preference-form-text-color;
  }

  .form-preference .el-checkbox__input.is-checked + .el-checkbox__label {
    color: $--dk-preference-form-text-color;
  }

  .form-preference .el-form-item {
    a {
      color: #dfdfdf;
      &:hover {
        color: #eee;
      }
      &:active {
        color: #eee;
      }
    }
  }

  /* Divider */
  .el-divider {
    background-color: #666;
  }
  .el-divider__text {
    background-color: $--dk-panel-background;
    color: #a7a7a7;
  }

  /* Popover */
  .el-popover {
    background-color: $--dk-popover-background;
    border-color: $--dk-popover-border-color;
  }

  .el-tag.el-tag--info.el-tag--light {
    background-color: #5b5b5b;
    border-color: #606060;
    color: #e6e6e6;
  }

  .el-tag__close.el-icon-close {
    color: #2d2d2d;
  }
}
