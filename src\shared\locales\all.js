import eleLocaleAr from 'element-ui/lib/locale/lang/ar'
import eleLocaleBg from 'element-ui/lib/locale/lang/bg'
import eleLocaleCa from 'element-ui/lib/locale/lang/ca'
import eleLocaleDe from 'element-ui/lib/locale/lang/de'
import eleLocaleEl from 'element-ui/lib/locale/lang/el'
import eleLocaleEn from 'element-ui/lib/locale/lang/en'
import eleLocaleEs from 'element-ui/lib/locale/lang/es'
import eleLocaleFa from 'element-ui/lib/locale/lang/fa'
import eleLocaleFr from 'element-ui/lib/locale/lang/fr'
import eleLocaleHu from 'element-ui/lib/locale/lang/hu'
import eleLocaleId from 'element-ui/lib/locale/lang/id'
import elelocaleIt from 'element-ui/lib/locale/lang/it'
import eleLocaleJa from 'element-ui/lib/locale/lang/ja'
import eleLocaleKo from 'element-ui/lib/locale/lang/ko'
import eleLocaleNb from 'element-ui/lib/locale/lang/nb-NO'
import eleLocaleNl from 'element-ui/lib/locale/lang/nl'
import eleLocalePl from 'element-ui/lib/locale/lang/pl'
import eleLocalePtBR from 'element-ui/lib/locale/lang/pt-br'
import eleLocaleRo from 'element-ui/lib/locale/lang/ro'
import eleLocaleRu from 'element-ui/lib/locale/lang/ru-RU'
import eleLocaleTh from 'element-ui/lib/locale/lang/th'
import eleLocaleTr from 'element-ui/lib/locale/lang/tr-TR'
import eleLocaleUk from 'element-ui/lib/locale/lang/ua'
import eleLocaleVi from 'element-ui/lib/locale/lang/vi'
import eleLocaleZhCN from 'element-ui/lib/locale/lang/zh-CN'
import eleLocaleZhTW from 'element-ui/lib/locale/lang/zh-TW'
import appLocaleAr from '@shared/locales/ar'
import appLocaleBg from '@shared/locales/bg'
import appLocaleCa from '@shared/locales/ca'
import appLocaleDe from '@shared/locales/de'
import appLocaleEl from '@shared/locales/el'
import appLocaleEnUS from '@shared/locales/en-US'
import appLocaleEs from '@shared/locales/es'
import appLocaleFa from '@shared/locales/fa'
import appLocaleFr from '@shared/locales/fr'
import appLocaleHu from '@shared/locales/hu'
import appLocaleId from '@shared/locales/id'
import applocaleIt from '@shared/locales/it'
import appLocaleJa from '@shared/locales/ja'
import appLocaleKo from '@shared/locales/ko'
import appLocaleNb from '@shared/locales/nb'
import appLocaleNl from '@shared/locales/nl'
import appLocalePl from '@shared/locales/pl'
import appLocalePtBR from '@shared/locales/pt-BR'
import appLocaleRo from '@shared/locales/ro'
import appLocaleRu from '@shared/locales/ru'
import appLocaleTh from '@shared/locales/th'
import appLocaleTr from '@shared/locales/tr'
import appLocaleUk from '@shared/locales/uk'
import appLocaleVi from '@shared/locales/vi'
import appLocaleZhCN from '@shared/locales/zh-CN'
import appLocaleZhTW from '@shared/locales/zh-TW'

// Please keep the locale key in alphabetical order.
/* eslint-disable quote-props */
const resources = {
  'ar': {
    translation: {
      ...eleLocaleAr,
      ...appLocaleAr
    }
  },
  'bg': {
    translation: {
      ...eleLocaleBg,
      ...appLocaleBg
    }
  },
  'ca': {
    translation: {
      ...eleLocaleCa,
      ...appLocaleCa
    }
  },
  'de': {
    translation: {
      ...eleLocaleDe,
      ...appLocaleDe
    }
  },
  'el': {
    translation: {
      ...eleLocaleEl,
      ...appLocaleEl
    }
  },
  'en-US': {
    translation: {
      ...eleLocaleEn,
      ...appLocaleEnUS
    }
  },
  'es': {
    translation: {
      ...eleLocaleEs,
      ...appLocaleEs
    }
  },
  'fa': {
    translation: {
      ...eleLocaleFa,
      ...appLocaleFa
    }
  },
  'fr': {
    translation: {
      ...eleLocaleFr,
      ...appLocaleFr
    }
  },
  'hu': {
    translation: {
      ...eleLocaleHu,
      ...appLocaleHu
    }
  },
  'id': {
    translation: {
      ...eleLocaleId,
      ...appLocaleId
    }
  },
  'it': {
    translation: {
      ...elelocaleIt,
      ...applocaleIt
    }
  },
  'ja': {
    translation: {
      ...eleLocaleJa,
      ...appLocaleJa
    }
  },
  'ko': {
    translation: {
      ...eleLocaleKo,
      ...appLocaleKo
    }
  },
  'nb': {
    translation: {
      ...eleLocaleNb,
      ...appLocaleNb
    }
  },
  'nl': {
    translation: {
      ...eleLocaleNl,
      ...appLocaleNl
    }
  },
  'pl': {
    translation: {
      ...eleLocalePl,
      ...appLocalePl
    }
  },
  'pt-BR': {
    translation: {
      ...eleLocalePtBR,
      ...appLocalePtBR
    }
  },
  'ro': {
    translation: {
      ...eleLocaleRo,
      ...appLocaleRo
    }
  },
  'ru': {
    translation: {
      ...eleLocaleRu,
      ...appLocaleRu
    }
  },
  'th': {
    translation: {
      ...eleLocaleTh,
      ...appLocaleTh
    }
  },
  'tr': {
    translation: {
      ...eleLocaleTr,
      ...appLocaleTr
    }
  },
  'uk': {
    translation: {
      ...eleLocaleUk,
      ...appLocaleUk
    }
  },
  'vi': {
    translation: {
      ...eleLocaleVi,
      ...appLocaleVi
    }
  },
  'zh-CN': {
    translation: {
      ...eleLocaleZhCN,
      ...appLocaleZhCN
    }
  },
  'zh-TW': {
    translation: {
      ...eleLocaleZhTW,
      ...appLocaleZhTW
    }
  }
}
/* eslint-enable quote-props */

export default resources
