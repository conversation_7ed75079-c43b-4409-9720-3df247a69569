import appLocaleAr from '@shared/locales/ar'
import appLocaleBg from '@shared/locales/bg'
import appLocaleCa from '@shared/locales/ca'
import appLocaleDe from '@shared/locales/de'
import appLocaleEl from '@shared/locales/el'
import appLocaleEnUS from '@shared/locales/en-US'
import appLocaleFa from '@shared/locales/fa'
import appLocaleFr from '@shared/locales/fr'
import appLocaleHu from '@shared/locales/hu'
import appLocaleId from '@shared/locales/id'
import appLocaleIt from '@shared/locales/it'
import appLocaleJa from '@shared/locales/ja'
import appLocaleNl from '@shared/locales/nl'
import appLocaleKo from '@shared/locales/ko'
import appLocalePl from '@shared/locales/pl'
import appLocalePtBR from '@shared/locales/pt-BR'
import appLocaleRo from '@shared/locales/ro'
import appLocaleRu from '@shared/locales/ru'
import appLocaleTh from '@shared/locales/th'
import appLocaleTr from '@shared/locales/tr'
import appLocaleUk from '@shared/locales/uk'
import appLocaleVi from '@shared/locales/vi'
import appLocaleZhCN from '@shared/locales/zh-CN'
import appLocaleZhTW from '@shared/locales/zh-TW'

// Please keep the locale key in alphabetical order.
/* eslint-disable quote-props */
const resources = {
  'ar': {
    translation: {
      ...appLocaleAr
    }
  },
  'bg': {
    translation: {
      ...appLocaleBg
    }
  },
  'ca': {
    translation: {
      ...appLocaleCa
    }
  },
  'de': {
    translation: {
      ...appLocaleDe
    }
  },
  'el': {
    translation: {
      ...appLocaleEl
    }
  },
  'en-US': {
    translation: {
      ...appLocaleEnUS
    }
  },
  'fa': {
    translation: {
      ...appLocaleFa
    }
  },
  'fr': {
    translation: {
      ...appLocaleFr
    }
  },
  'hu': {
    translation: {
      ...appLocaleHu
    }
  },
  'id': {
    translation: {
      ...appLocaleId
    }
  },
  'it': {
    translation: {
      ...appLocaleIt
    }
  },
  'ja': {
    translation: {
      ...appLocaleJa
    }
  },
  'nl': {
    translation: {
      ...appLocaleNl
    }
  },
  'ko': {
    translation: {
      ...appLocaleKo
    }
  },
  'pl': {
    translation: {
      ...appLocalePl
    }
  },
  'pt-BR': {
    translation: {
      ...appLocalePtBR
    }
  },
  'ro': {
    translation: {
      ...appLocaleRo
    }
  },
  'ru': {
    translation: {
      ...appLocaleRu
    }
  },
  'th': {
    translation: {
      ...appLocaleTh
    }
  },
  'tr': {
    translation: {
      ...appLocaleTr
    }
  },
  'uk': {
    translation: {
      ...appLocaleUk
    }
  },
  'vi': {
    translation: {
      ...appLocaleVi
    }
  },
  'zh-CN': {
    translation: {
      ...appLocaleZhCN
    }
  },
  'zh-TW': {
    translation: {
      ...appLocaleZhTW
    }
  }
}
/* eslint-enable quote-props */

export default resources
