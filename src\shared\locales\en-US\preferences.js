export default {
  'basic': 'Basic',
  'advanced': 'Advanced',
  'lab': 'Lab',
  'save': 'Save & Apply',
  'save-success-message': 'Preferences saved successfully',
  'save-fail-message': 'Preferences failed to save',
  'discard': 'Discard',
  'startup': 'Startup',
  'open-at-login': 'Open at login',
  'keep-window-state': 'Keep size and position of the window when exited',
  'auto-resume-all': 'Automatically resume all unfinished tasks',
  'default-dir': 'Default Path',
  'mas-default-dir-tips': 'Due to sandbox permission restrictions of the App Store, the default download directory is recommended to be set to ~/Downloads',
  'transfer-settings': 'Transmission',
  'transfer-speed-upload': 'Upload limit',
  'transfer-speed-download': 'Download limit',
  'transfer-speed-unlimited': 'Unlimited',
  'bt-settings': 'BitTorrent',
  'bt-save-metadata': 'Save magnet link as torrent file',
  'bt-auto-download-content': 'Automatically download magnet and torrent content',
  'bt-force-encryption': 'BT force encryption',
  'keep-seeding': 'Keep seeding until manually stopped',
  'seed-ratio': 'Seed Ratio',
  'seed-time': 'Seed Time',
  'seed-time-unit': 'minutes',
  'task-manage': 'Task Management',
  'max-concurrent-downloads': 'Maximum active tasks',
  'max-connection-per-server': 'Maximum connections per server',
  'new-task-show-downloading': 'Automatically show downloading after adding task',
  'no-confirm-before-delete-task': 'No confirmation is required before deleting task',
  'continue': 'Continue',
  'task-completed-notify': 'Notify after download is complete',
  'auto-purge-record': 'Automatically purge download records when exiting app',
  'ui': 'UI',
  'appearance': 'Appearance',
  'theme-auto': 'Auto',
  'theme-light': 'Light',
  'theme-dark': 'Dark',
  'auto-hide-window': 'Auto Hide Window',
  'run-mode': 'Run As',
  'run-mode-standard': 'Standard Application',
  'run-mode-tray': 'Tray Application',
  'run-mode-hide-tray': 'Hide Tray Application',
  'tray-speedometer': 'Show the real-time speed in the menu bar tray',
  'show-progress-bar': 'Show download progress bar',
  'language': 'Language',
  'change-language': 'Change language',
  'hide-app-menu': 'Hide App Menu (Windows & Linux Only)',
  'proxy': 'Proxy',
  'enable-proxy': 'Enable Proxy',
  'proxy-bypass-input-tips': 'Bypass proxy settings for these Hosts and Domains, one per line',
  'proxy-scope-download': 'Download',
  'proxy-scope-update-app': 'Update Application',
  'proxy-scope-update-trackers': 'Update Trackers',
  'proxy-tips': 'View Proxy Manual',
  'bt-tracker': 'Tracker Servers',
  'bt-tracker-input-tips': 'Tracker servers, one per line',
  'bt-tracker-tips': 'Recommended: ',
  'sync-tracker-tips': 'Sync',
  'auto-sync-tracker': 'Update tracker list every day automatically',
  'port': 'Listen Ports',
  'bt-port': 'BT Listen Port',
  'dht-port': 'DHT Listen Port',
  'security': 'Security',
  'rpc': 'RPC',
  'rpc-listen-port': 'RPC Listen Port',
  'rpc-secret': 'RPC Secret',
  'rpc-secret-tips': 'View RPC Secret Manual',
  'developer': 'Developer',
  'user-agent': 'User-Agent',
  'mock-user-agent': 'Mock User-Agent',
  'aria2-conf-path': 'Built-in aria2.conf path',
  'app-log-path': 'App log path',
  'download-session-path': 'Download session path',
  'session-reset': 'Reset download session',
  'session-reset-confirm': 'Are you sure you want to reset download session?',
  'factory-reset': 'Factory Reset',
  'factory-reset-confirm': 'Are you sure you want to revert to factory settings?',
  'lab-warning': '⚠️ Enabling lab features may result in app crash or data loss, decide at you own risk!',
  'download-protocol': 'Protocols',
  'protocols-default-client': 'Set as the default client for the following protocols',
  'protocols-magnet': 'Magnet [ magnet:// ]',
  'protocols-thunder': 'Thunder [ thunder:// ]',
  'browser-extensions': 'Extensions',
  'baidu-exporter': 'BaiduExporter',
  'browser-extensions-tips': 'Provided by the community, ',
  'baidu-exporter-help': 'Click here for usage',
  'auto-update': 'Auto Update',
  'auto-check-update': 'Automatically check for updates',
  'last-check-update-time': 'Last checked for an update',
  'not-saved': 'Preferences not saved',
  'not-saved-confirm': 'The modified preferences will be lost, are you sure you want to leave?'
}
