export default {
  'active': 'İndiriliyor',
  'waiting': 'Be<PERSON><PERSON>yo<PERSON>',
  'stopped': 'Durdu',
  'new-task': '<PERSON><PERSON>',
  'new-bt-task': '<PERSON>ni BT Görevi',
  'open-file': 'Torrent Dosyasını Aç...',
  'uri-task': 'URL',
  'torrent-task': 'Torrent',
  'uri-task-tips': 'Her bir satır için bir görev (magnet destekli)',
  'thunder-link-tips': 'İpucu: Thunder bağlantıları kod çözme işleminden sonra indirilemeyebilir',
  'new-task-uris-required': 'Lütfen en az bir geçerli kaynak URL adresi girin',
  'new-task-torrent-required': 'Lütfen bir torrent dosyası seçin',
  'file-name': 'Dosya Adı',
  'file-extension': 'uzantı',
  'file-size': 'Boyut',
  'file-completed-size': 'İndirildi',
  'selected-files-sum': 'Seçildi: {{selectedFilesCount}} dosya sayısı, total {{selectedFilesTotalSize}}',
  'select-at-least-one': 'Lütfen en az bir dosya seçin',
  'task-gid': 'GID',
  'task-name': 'Görev Adı',
  'task-out': 'Dosya Adı',
  'task-out-tips': 'Opsiyonel',
  'task-split': 'Parça',
  'task-dir': 'Yol',
  'pause-task': 'Görevi Durdur',
  'task-ua': 'UA',
  'task-user-agent': 'User-Agent',
  'task-authorization': 'Yetkilendirme',
  'task-referer': 'Yönlendiren',
  'task-cookie': 'Çerez',
  'task-proxy': 'Proxy',
  'task-error-info': 'Hata',
  'task-piece': 'Parça',
  'task-piece-length': 'Parça Boyutu',
  'task-num-pieces': 'Adet',
  'task-bittorrent-info': 'Torrent Bilgisi',
  'task-info-hash': 'Hash',
  'task-bittorrent-creation-date': 'Oluşturulma tarihi',
  'task-bittorrent-comment': 'Yorum Yap',
  'task-progress-info': 'İlerleme',
  'task-status': 'Durum',
  'task-num-seeders': 'Ekme makineleri',
  'task-connections': 'Bağlantılar',
  'task-file-size': 'Boyut',
  'task-download-speed': 'İndirme hızı',
  'task-upload-speed': 'Yükleme hızı',
  'task-download-length': 'İndirildi',
  'task-upload-length': 'Yüklendi',
  'task-ratio': 'Oran',
  'task-peer-host': 'Ev sahibi',
  'task-peer-ip': 'IP',
  'task-peer-client': 'Müşteri',
  'navigate-to-downloading': 'İndirilenlere git',
  'show-advanced-options': 'Gelişmiş ayarlar',
  'copyright-warning': 'Telif Uyarısı',
  'copyright-warning-message': 'İndirmek istediğiniz dosya telif hakkıyla korunan ses veya videoya ait olabilir, lütfen telif hakkı lisansına sahip olduğunuzdan emin olun.',
  'copyright-yes': 'Evet, sahibim',
  'copyright-no': 'Hayır',
  'copyright-error-message': 'Telif hakkı sorunları nedeniyle görev ekleme işlemi başarısız oldu',
  'pause-task-success': '"{{taskName}}" görevi durduruldu',
  'pause-task-fail': '"{{taskName}}" görevi durdurulamadı',
  'resume-task': 'Görevi sürdür',
  'resume-task-success': '"{{taskName}}" devam ettirildi',
  'resume-task-fail': '"{{taskName}}" devam ettirilemedi',
  'delete-task': 'Görevi sil',
  'delete-selected-tasks': 'Seçilen görevleri sil',
  'delete-task-confirm': '"{{taskName}}" adlı görevi silmek istediğinizden emin misiniz?',
  'batch-delete-task-confirm': '{{count}} indirme görevini toplu olarak kaldırmak istediğinizden emin misiniz?',
  'delete-task-label': 'Dosyalar ile birlikte sil',
  'delete-task-success': '"{{taskName}}" silindi',
  'delete-task-fail': '"{{taskName}}" silinemedi',
  'remove-task-file-fail': 'Görev dosyaları silinemedi, lütfen manuel olarak silin',
  'remove-task-config-file-fail': 'Görev yapılandırma dosyası silinemedi, lütfen manuel olarak silin',
  'move-task-up': 'Görevi yukarı taşı',
  'move-task-down': 'Görevi aşağı taşı',
  'pause-all-task': 'Bütün görevleri durdur',
  'pause-all-task-success': 'Bütün görevleri durdurma başarılı',
  'pause-all-task-fail': 'Bütün görevleri durdurma başarısız',
  'resume-all-task': 'Bütün görevleri sürdür',
  'resume-all-task-success': 'Bütün görevleri sürdürme başarılı',
  'resume-all-task-fail': 'Bütün görevleri sürdürme başarısız',
  'select-all-task': 'Tüm görevi seçin',
  'clear-recent-tasks': 'Son görevleri temizle',
  'purge-record': 'Görev Kaydını Temizle',
  'purge-record-success': 'Görev Kaydını Temizleme başarılı',
  'purge-record-fail': 'Görev Kaydını Temizleme başarısız',
  'batch-delete-task-success': 'Toplu işteki görevleri başarıyla silin',
  'batch-delete-task-fail': 'Toplu işteki görevler silinemedi',
  'refresh-list': 'Görev listesini yenile',
  'no-task': 'Görev yok',
  'copy-link': 'Bağlantıyı kopyala',
  'copy-link-success': 'Bağlantı Kopyalama başarılı',
  'remove-record': 'Görev kaydını kaldır',
  'remove-record-confirm': '"{{taskName}}" görevinin kaydın kaldırmak istediğinizden emin misiniz?',
  'remove-record-label': 'Dosyalar ile birlikte kaldır',
  'remove-record-success': '"{{taskName}}" kayıtları kaldırıldı',
  'remove-record-fail': '"{{taskName}}" kayıtları kaldırılamadı',
  'show-in-folder': 'Görevi klasörde göster',
  'file-not-exist': 'Dosya yok ya da silinmiş',
  'file-path-error': 'Dosya yolu hatası',
  'opening-task-message': '"{{taskName}}" açılıyor ...',
  'get-task-name': 'Görev ismi getir...',
  'remaining-prefix': 'Kalan Süre',
  'select-torrent': 'Torrent dosyasını sürükleyin ya da seçmek için tıklayın',
  'task-info-dialog-title': '{{title}} hakkında bilgiler',
  'download-start-message': '{{taskName}} görevi başlatıldı',
  'download-pause-message': '{{taskName}} görevi duraklatıldı',
  'download-stop-message': '{{taskName}} görevi durduruldu',
  'download-error-message': '{{taskName}} görevinde hata oluştu',
  'download-complete-message': '{{taskName}} görevi tamamlandı',
  'download-complete-notify': 'İndirme bitti',
  'bt-download-complete-message': '{{taskName}} indirme tamamlandı, tohumlama...',
  'bt-download-complete-notify': 'BT Indirme tamamlandı, tohumlama...',
  'bt-download-complete-tips': 'Ipuçları: Eğer tohumlama sona erdirmek için görev durdurabilirsiniz',
  'bt-stopping-seeding-tip': 'Ekim işlemini durdurmak, bağlantıyı kesmek biraz zaman alacak, lütfen bekleyin...',
  'download-fail-message': '{{taskName}} görevi indirilemedi',
  'download-fail-notify': 'İndirme başarısız'
}
