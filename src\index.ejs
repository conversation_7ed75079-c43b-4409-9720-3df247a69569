<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>Motrix</title>
    <% if (htmlWebpackPlugin.options.nodeModules) { %>
      <!-- Add `node_modules/` to global paths so `require` works properly in development -->
      <script>
        require('module').globalPaths.push('<%= htmlWebpackPlugin.options.nodeModules.replace(/\\/g, '\\\\') %>')
      </script>
    <% } %>
  </head>

  <style>
    .skeleton-aside {
      background-color: rgba(0, 0, 0, 0.8);
      width: 78px;
    }
    .skeleton-subnav {
      background-color: #f4f5f7;
      width: 200px;
    }
    .skeleton-main {
      background-color: #fff;
    }

    @media (prefers-color-scheme: dark) {
      .skeleton-aside {
        background-color: rgba(0, 0, 0, 0.9);
      }
      .skeleton-subnav {
        background-color: #2D2D2D;
      }
      .skeleton-main {
        background-color: #343434;
      }
    }
  </style>

  <body>
    <div id="app">
      <div class="title-bar"></div>
      <section class="el-container" id="container">
        <aside class="el-aside skeleton-aside hidden-sm-and-down">
        </aside>
        <aside class="el-aside skeleton-subnav hidden-xs-only">
        </aside>
        <section class="el-container skeleton-main">
        </section>
      </section>
    </div>
    <!-- Set `__static` path to static files in production -->
    <% if (!htmlWebpackPlugin.options.isBrowser && !htmlWebpackPlugin.options.isDev) { %>
      <script>
        window.__static = require('path').join(__dirname, '/static').replace(/\\/g, '\\\\')
      </script>
    <% } %>

    <!-- webpack builds are automatically injected -->
  </body>
</html>
