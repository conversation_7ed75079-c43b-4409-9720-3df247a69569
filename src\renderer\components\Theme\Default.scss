html,
body {
  height: 100%;
  padding: 0;
}

body {
  font-family: "Monospaced Number", "Chinese Quote", -apple-system,
    BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-variant: tabular-nums;
  font-size: $--font-size-medium;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.4);
}

::-webkit-scrollbar-thumb:window-inactive {
  background-color: rgba(0, 0, 0, 0.25);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

::-webkit-resizer {
  display: none;
}

img {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
}

iframe {
  border: 0;
}

.clearfix {
  @include clearfix();
}

/* Element UI
-------------------------- */
.el-progress-bar__inner {
  transition: all 0.4s cubic-bezier(0.08, 0.82, 0.17, 1) 0s;
}

.el-progress--line.is-text {
  .el-progress-bar__inner::before {
    content: "";
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    border-radius: 10px;
    animation: mo-progress-active 2.4s cubic-bezier(0.23, 1, 0.32, 1) infinite;
  }
}

@keyframes mo-progress-active {
  0% {
    opacity: 0.1;
    width: 0;
  }
  20% {
    opacity: 0.5;
    width: 0;
  }
  100% {
    opacity: 0;
    width: 100%;
  }
}

.el-message-box__wrapper {
  outline: none;
}

.el-message__content {
  line-height: 18px;
  word-break: break-all;
  a {
    color: $--link-color;
  }
}

.tab-title-dialog {
  .el-dialog__header {
    padding: 0 20px;
    position: relative;
    z-index: 10;
  }
  .el-dialog__body {
    padding-top: 10px;
    padding-bottom: 10px;
  }
}

.el-form-item--mini .el-form-item__info {
  padding-top: 1px;
}

.el-form-item__info {
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  color: $--color-info;
  > a {
    margin-right: 10px;
  }
}

.el-input-number.el-input-number--mini {
  &.is-controls-left .el-input__inner {
    padding-left: 34px;
    padding-right: 5px;
  }
  &.is-controls-right .el-input__inner {
    padding-left: 5px;
    padding-right: 34px;
  }
}

.el-drawer__container {
  outline: none;
}

/* App Main
-------------------------- */
#app,
#container {
  height: 100%;
  background-color: $--app-background;
}

.draggable {
  -webkit-app-region: drag;
  user-select: none;
}

.non-draggable {
  -webkit-app-region: no-drag;
}

.aside {
  background-color: $--aside-background;
  color: $--aside-text-color;
}

.subnav {
  background-color: $--subnav-background;
  color: $--subnav-text-color;
}

.main {
  background-color: $--main-background;
}

.subnav-inner {
  margin-top: 44px;
  padding: 0 16px;
  user-select: none;

  h3 {
    font-size: 16px;
    color: $--subnav-title-color;
    font-weight: normal;
    line-height: 24px;
    margin: 0 0 28px;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    cursor: default;

    li {
      margin-bottom: 8px;
      padding: 8px 10px;
      font-size: 0;
      line-height: 20px;
      border-radius: 3px;
      cursor: pointer;

      i,
      span {
        display: inline-block;
        vertical-align: middle;
        font-size: 14px;
      }

      &:hover,
      &.active {
        background-color: $--subnav-active-background;

        i,
        span,
        svg {
          color: $--subnav-active-text-color;
        }
      }
    }
  }
}

.subnav-icon {
  padding: 2px;
  height: 16px;
  margin-right: 12px;

  svg {
    width: 16px;
    height: 16px;
  }
}

.content {
  padding: 0;
}

.panel {
  background: $--panel-background;
  .panel-header {
    position: relative;
    padding: 44px 0 12px;
    margin: 0 16px;
    border-bottom: 2px solid $--panel-border-color;
    user-select: none;
    h4 {
      margin: 0;
      color: $--panel-title-color;
      font-size: 16px;
      font-weight: normal;
      line-height: 24px;
    }
  }
  .panel-content {
    position: relative;
    padding: 0;
    height: 100%;
  }
}

.form-actions {
  background: $--form-actions-background;
}

.mo-table-wrapper {
  border: 1px solid $--table-border-color;
  border-bottom: none;
  overflow-x: hidden;
  overflow-y: auto;
  .el-table th {
    padding: 2px 0;
  }
}

.graphic-box {
  padding: 0.5rem 0.375rem;
  margin-bottom: 1.5rem;
  font-size: 0;
  line-height: 0;
  border: 1px solid $--task-detail-box-border;
  border-radius: $--border-radius-base;
  &> svg {
    display: block;
    margin: 0 auto;
  }
}

.form-static-value {
  word-break: break-all;
  color: $--input-font-color;
}

@media only screen and (max-width:567px) {
  .hidden-xs-only {
    display:none !important
  }
}

@media only screen and (min-width: 568px) {
  .panel {
    .panel-header {
      margin-left: 36px;
      margin-right: 36px;
    }
  }
  .task-list {
    padding-left: 36px;
    padding-right: 36px;
  }
  .form-preference {
    padding-left: 36px;
  }
  .form-actions {
    padding: 24px 36px;
  }
  .mo-speedometer {
    right: 36px;
  }
  .hidden-sm-and-up {
    display: none !important;
  }
}
@media only screen and (min-width: 568px) and (max-width: 991px) {
  .hidden-sm-only {
    display: none !important;
  }
}

@media only screen and (max-width: 791px) {
  .hidden-sm-and-down {
    display: none !important;
  }
}

@media only screen and (min-width: 792px) {
  .hidden-md-and-up {
    display: none !important;
  }
}
