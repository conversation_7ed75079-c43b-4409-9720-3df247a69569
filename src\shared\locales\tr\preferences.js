export default {
  'basic': '<PERSON><PERSON>',
  'advanced': '<PERSON><PERSON>şmiş',
  'lab': '<PERSON><PERSON><PERSON>',
  'save': 'Kaydet & Uygula',
  'save-success-message': '<PERSON>rc<PERSON><PERSON>i başarıyla kaydedin',
  'save-fail-message': '<PERSON>rc<PERSON><PERSON>i kaydetme başarısız oldu',
  'discard': 'İptal Et',
  'startup': 'Başlangıçta',
  'open-at-login': '<PERSON><PERSON><PERSON> sırasında aç',
  'keep-window-state': 'Pencerenin boyutunu ve konumunu geri yükleyin',
  'auto-resume-all': 'Tüm bitmemiş görevleri otomatik olarak devam ettir',
  'default-dir': 'Varsayılan Klasör',
  'mas-default-dir-tips': 'App Store\'un sanal alan izinleri kısıtlamaları nedeniyle, varsayılan indirme dizininin İndirilenler dizinine ayarlanması önerilir.',
  'transfer-settings': 'İletim',
  'transfer-speed-upload': 'Yükleme limiti',
  'transfer-speed-download': 'İndirme limiti',
  'transfer-speed-unlimited': 'Sınırsız',
  'bt-settings': 'BitTorrent',
  'bt-save-metadata': 'Mıknatıs bağlantısını torrent dosyası olarak kaydedin',
  'bt-auto-download-content': 'Magnet ve torrent içeriğini otomatik olarak indirin',
  'bt-force-encryption': 'BT Zorunlu Şifreleme',
  'keep-seeding': 'Manuel olarak durdurana kadar tohumlamaya devam edin',
  'seed-ratio': 'Tohum Oranı',
  'seed-time': 'Tohum Zamanı',
  'seed-time-unit': 'dakika',
  'task-manage': 'Görev Yöneticisi',
  'max-concurrent-downloads': 'Maksimum aktif görev',
  'max-connection-per-server': 'Sunucu başına maksimum bağlantı',
  'new-task-show-downloading': 'Görev ekledikten sonra indirmeyi otomatik göster',
  'no-confirm-before-delete-task': 'Görevi silmeden önce onay gerekmez',
  'continue': 'Devamlı',
  'task-completed-notify': 'İndirme bittikten sonra bildirim göster',
  'auto-purge-record': 'Auto purge download record when app exit',
  'ui': 'Kullanıcı Arayüzü',
  'appearance': 'Görünüş',
  'theme-auto': 'Otomatik',
  'theme-light': 'Açık',
  'auto-hide-window': 'Pencereleri otomatik gizle',
  'run-mode': 'Olarak çalıştırmak',
  'run-mode-standard': 'Standart Uygulama',
  'run-mode-tray': 'Tepsi Uygulaması',
  'theme-dark': 'Koyu',
  'run-mode-hide-tray': 'Tepsi uygulamasını gizle',
  'tray-speedometer': 'Menü çubuğu tepsisi gerçek zamanlı hızı gösterir',
  'show-progress-bar': 'İndirme ilerleme çubuğunu göster',
  'language': 'Dil',
  'change-language': 'Dil değiştir',
  'hide-app-menu': 'Uygulama menüsünü göster (Windows & Linux için)',
  'proxy': 'Proxy',
  'enable-proxy': 'Proxy etkinleştir',
  'proxy-bypass-input-tips': 'Her bir satırda bir tane olacak şekilde bu Ana Bilgisayarlar ve Alanlar için proxy ayarlarını atlayın',
  'proxy-scope-download': 'İndirme',
  'proxy-scope-update-app': 'Uygulamayı Güncelle',
  'proxy-scope-update-trackers': 'İzleyicileri Güncelle',
  'proxy-tips': 'Proxy Kılavuzunu Görüntüle',
  'bt-tracker': 'İzleyici Sunucular',
  'bt-tracker-input-tips': 'İzleyici sunucusu, her satıra bir tane',
  'bt-tracker-tips': 'Tavsiye et:',
  'sync-tracker-tips': 'Senkronizasyon',
  'auto-sync-tracker': 'İzleyici listesini her gün otomatik olarak güncelleyin',
  'port': 'Bağlantı noktalarını dinleyin',
  'bt-port': 'BT dinleme bağlantı noktası',
  'dht-port': 'DHT dinleme bağlantı noktası',
  'security': 'Güvenlik',
  'rpc': 'RPC',
  'rpc-listen-port': 'RPC Dinleme Portu',
  'rpc-secret': 'RPC sırrı',
  'rpc-secret-tips': 'RPC gizli kılavuzunu görüntüle',
  'developer': 'Geliştirici',
  'user-agent': 'User-Agent',
  'mock-user-agent': 'Sahte Kullanıcı Kimliği (User-Agent)',
  'aria2-conf-path': 'Dahili aria2.conf yolu',
  'app-log-path': 'Uygulama log yolu',
  'download-session-path': 'Oturum yolunu indir',
  'factory-reset': 'Fabrika ayarlarına dön',
  'factory-reset-confirm': 'Fabrika ayarlarına geri dönmek istediğinize emin misiniz?',
  'lab-warning': '⚠️ Deneysel özellikleri etkinleştirmek uygulamanın çökmesine veya veri kaybına neden olabilir, kendiniz karar verin!',
  'download-protocol': 'Protokol',
  'protocols-default-client': 'Aşağıdaki protokoller için varsayılan istemci olarak ayarla',
  'protocols-magnet': 'Mıknatıs [ magnet:// ]',
  'protocols-thunder': 'gök gürültüsü [ thunder:// ]',
  'browser-extensions': 'Eklentiler',
  'baidu-exporter': 'BaiduExporter',
  'browser-extensions-tips': 'Topluluk tarafından sağlanan, ',
  'baidu-exporter-help': 'Kullanım detayları için buraya tıklayın',
  'auto-update': 'Otomatik güncelleme',
  'auto-check-update': 'Otomatik Kontrol Güncellemesi',
  'last-check-update-time': 'Son Kontrol Güncelleme Saati',
  'not-saved': 'Tercihler kaydedilmedi',
  'not-saved-confirm': 'Değiştirilen tercihler kaybolacak, ayrılacağınızdan emin misiniz?'
}
