name: Build/release

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  release:
    runs-on: ${{ matrix.os }}

    # Platforms to build on/for
    strategy:
      matrix:
        os: [macos-latest, ubuntu-latest, windows-latest]

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v3

      - name: Install Node.js, NPM and Yarn
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install Snapcraft
        uses: samuelmeuli/action-snapcraft@v2
        # Only install Snapcraft on Ubuntu
        if: startsWith(matrix.os, 'ubuntu')
        env:
          # Snapcraft
          SNAPCRAFT_STORE_CREDENTIALS: ${{ secrets.snapcraft_token }}

      - name: Test Snapcraft
        if: startsWith(matrix.os, 'ubuntu')
        run: snapcraft --help

      - name: Build/release Electron app
        uses: motrixapp/action-electron-builder@v2
        with:
          build_script_name: 'build:github'
          # GitHub token, automatically provided to the action
          # (No need to define this secret in the repo settings)
          github_token: ${{ secrets.github_token }}

          # macOS code signing certificate
          mac_certs: ${{ secrets.mac_certs }}
          mac_certs_password: ${{ secrets.mac_certs_password }}

          # If the commit is tagged with a version (e.g. "v1.0.0"),
          # release the app after building
          release: ${{ vars.skip_publish != 'true' }}
        env:
          # Snapcraft
          SNAPCRAFT_STORE_CREDENTIALS: ${{ secrets.snapcraft_token }}
          # macOS notarization
          TEAM_ID: ${{ secrets.team_id }}
          APPLE_ID: ${{ secrets.apple_id }}
          APPLE_APP_SPECIFIC_PASSWORD: ${{ secrets.apple_app_specific_password }}
