export default {
  'task-list': 'Tasks',
  'add-task': 'Add Task',
  'about': 'About Motrix',
  'preferences': 'Preferences...',
  'check-for-updates': 'Check for Updates...',
  'check-updates-now': 'Check now',
  'checking-for-updates': 'Checking for updates ...',
  'check-for-updates-title': 'Check for Updates',
  'update-available-message': 'A newer version of Motrix is available, update now?',
  'update-not-available-message': 'You are up-to-date!',
  'update-downloaded-message': 'Ready to install...',
  'update-error-message': 'Update Error',
  'engine-damaged-message': 'The engine is damaged, please reinstall : (',
  'engine-missing-message': 'The engine is missing, please reinstall : (',
  'system-error-title': 'System Error',
  'system-error-message': 'Application startup failed: {{message}}',
  'hide': 'Hide Motrix',
  'hide-others': 'Hide Others',
  'unhide': 'Show All',
  'show': 'Show Motrix',
  'quit': 'Quit Motrix',
  'under-development-message': 'Sorry, this feature is under development...',
  'yes': 'Yes',
  'no': 'No',
  'save': 'Save',
  'reset': 'Discard',
  'cancel': 'Cancel',
  'submit': 'Submit',
  'gt1d': '> 1 day',
  'hour': 'h',
  'minute': 'm',
  'second': 's'
}
