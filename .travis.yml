sudo: required
dist: trusty

language: c

jobs:
  include:
  - os: osx
    osx_image: xcode11.3
  - os: linux
    env: CC=clang CXX=clang++ npm_config_clang=1
    compiler: clang

cache:
  directories:
  - node_modules
  - $HOME/.cache/electron
  - $HOME/.cache/electron-builder

addons:
  apt:
    packages:
    - libgnome-keyring-dev
    - icnsutils
    - rpm

before_install:
- if [[ "$TRAVIS_OS_NAME" == "linux" ]]; then sudo apt-get install --no-install-recommends -y icnsutils graphicsmagick xz-utils; fi

install:
- nvm install 12.14.1
- source ~/.bashrc
- npm install -g xvfb-maybe
- npm install

script:
- npm run release

before_cache:
  - rm -rf $HOME/.cache/electron-builder/wine

branches:
  only:
  - master
