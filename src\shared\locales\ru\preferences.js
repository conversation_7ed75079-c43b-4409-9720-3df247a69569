export default {
  'basic': 'Основные',
  'advanced': 'Расширенные',
  'lab': 'Лаборатория',
  'save': 'Сохранить и применить',
  'save-success-message': 'Настройки сохранены успешно',
  'save-fail-message': 'Ошибка при сохранении настроек',
  'discard': 'Отмена',
  'startup': 'Запускать',
  'open-at-login': 'Запускать програму вместе со стартом операционной системы',
  'keep-window-state': 'Во время закрытия приложения, сохранять размер и положение окна',
  'auto-resume-all': 'Автоматически возобновлять все незавершенные задачи',
  'default-dir': 'Папка по умолчанию',
  'mas-default-dir-tips': 'Из-за ограничения в App Store, рекомендуется устанавливать путь по умолчанию как ~/Downloads',
  'transfer-settings': 'коробка передач',
  'transfer-speed-upload': 'Лимит отдачи',
  'transfer-speed-download': 'Лимит загрузки',
  'transfer-speed-unlimited': 'Безлимитно',
  'bt-settings': 'BitTorrent',
  'bt-save-metadata': 'Сохранить магнитную ссылку как торрент-файл',
  'bt-auto-download-content': 'Автоматически загружать магнит и торрент',
  'bt-force-encryption': 'Принудительное шифрование BT',
  'keep-seeding': 'Продолжайте посев, пока не остановите его вручную',
  'seed-ratio': 'Соотношение семян',
  'seed-time': 'Время посева',
  'seed-time-unit': 'минут',
  'task-manage': 'Менеджер задач',
  'max-concurrent-downloads': 'Максимум активных задач',
  'max-connection-per-server': 'Максимум соединений на сервер',
  'new-task-show-downloading': 'Автоматически отображать задачу после ее добавления',
  'no-confirm-before-delete-task': 'Перед удалением задачи подтверждение не требуется',
  'continue': 'Продолжить',
  'task-completed-notify': 'Сообщение после окончания загрузки',
  'auto-purge-record': 'Автоматически чистить записи о загрузках после закрытия приложения',
  'ui': 'UI',
  'appearance': 'Внешний вид',
  'theme-auto': 'Автоматически',
  'theme-light': 'Светлый',
  'theme-dark': 'Темный',
  'auto-hide-window': 'Автоскрытие окон',
  'run-mode': 'Запускать как...',
  'run-mode-standard': 'Стандартное приложение',
  'run-mode-tray': 'Приложение в трее',
  'run-mode-hide-tray': 'Скрыть приложение в трее',
  'tray-speedometer': 'Панель меню отображает скорость в реальном времени',
  'show-progress-bar': 'Показать индикатор загрузки',
  'language': 'Язык',
  'change-language': 'Сменить язык',
  'hide-app-menu': 'Скрыть меню приложения (только для Windows и Linux)',
  'proxy': 'Proxy',
  'enable-proxy': 'Использовать Proxy',
  'proxy-bypass-input-tips': 'Обойти настройки прокси для этих хостов и доменов, по одному в строке',
  'proxy-scope-download': 'Скачать',
  'proxy-scope-update-app': 'Обновить приложение',
  'proxy-scope-update-trackers': 'Обновить трекеры',
  'proxy-tips': 'Посмотреть руководство по прокси',
  'bt-tracker': 'Tracker Сервер',
  'bt-tracker-input-tips': 'Tracker сервера, один в строку',
  'bt-tracker-tips': 'Рекомендовано: ',
  'sync-tracker-tips': 'Синхронизация',
  'auto-sync-tracker': 'Обновлять список трекеров каждый день автоматически',
  'port': 'Порты прослушивания',
  'bt-port': 'Порт прослушивания BT',
  'dht-port': 'Порт прослушивания DHT',
  'security': 'Безопастность',
  'rpc': 'RPC',
  'rpc-listen-port': 'Порт прослушивания RPC',
  'rpc-secret': 'RPC Secret',
  'rpc-secret-tips': 'Смотреть инструкцию RPC Secret',
  'developer': 'Разработчик',
  'user-agent': 'User-Agent',
  'mock-user-agent': 'Макет User-Agent',
  'aria2-conf-path': 'Встроенный путь к aria2.conf',
  'app-log-path': 'Путь к журналу приложения',
  'download-session-path': 'Загрузить путь сессии',
  'factory-reset': 'Настройки по умолчанию',
  'factory-reset-confirm': 'Вы уверены, что хотите вернуться к настройкам по умолчанию?',
  'lab-warning': '⚠️ Включение функций лаборатории может привести к сбоям приложения и потери данных. Вы действуете на свой страх и риск!',
  'download-protocol': 'Протоколы',
  'protocols-default-client': 'Установить как клиента по умолчанию для следующих протоколов',
  'protocols-magnet': 'Magnet [ magnet:// ]',
  'protocols-thunder': 'Thunder [ thunder:// ]',
  'browser-extensions': 'Расширения',
  'baidu-exporter': 'BaiduExporter',
  'browser-extensions-tips': 'Предоставляются сообществом, ',
  'baidu-exporter-help': 'Нажмите здесь для использования',
  'auto-update': 'Автоматическое обновление',
  'auto-check-update': 'Автоматически проверять обновления',
  'last-check-update-time': 'Последняя проверка на обновления прошла в',
  'not-saved': 'Настройки не сохранены',
  'not-saved-confirm': 'Измененные настройки будут потеряны, вы обязательно уйдете?'
}
