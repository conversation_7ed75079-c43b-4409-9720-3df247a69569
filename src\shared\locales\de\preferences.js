export default {
  'basic': 'Standard',
  'advanced': '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  'lab': 'Experimentell',
  'save': 'Speichern & übernehmen',
  'save-success-message': 'Einstellungen erfolgreich speichern',
  'save-fail-message': 'Speichern der Einstellungen fehlgeschlagen',
  'discard': 'Verwerfen',
  'startup': 'Startup',
  'open-at-login': '<PERSON><PERSON>',
  'keep-window-state': 'Stellen Sie die Größe und Position des Fensters wieder her',
  'auto-resume-all': 'Alle nicht abgeschlossenen Aufgaben automatisch fortsetzen',
  'default-dir': 'Standardpfad',
  'mas-default-dir-tips': 'Aufgrund der Einschränkungen durch Sandbox-Berechtigungen im App Store wird der Download Ordner als Standard empfohlen',
  'transfer-settings': 'Übertragung',
  'transfer-speed-upload': 'Upload-Limit',
  'transfer-speed-download': 'Download-Limit',
  'transfer-speed-unlimited': 'Unbegrenzt',
  'bt-settings': 'BitTorrent',
  'bt-save-metadata': 'Magnetlink als Torrent-Datei speichern',
  'bt-auto-download-content': 'Laden Sie den Magneten- und Torrent-Inhalt automatisch herunter',
  'bt-force-encryption': 'BT Zwangskodierung',
  'keep-seeding': 'Setzen Sie die Aussaat fort, bis Sie sie manuell stoppen',
  'seed-ratio': 'Samenverhältnis',
  'seed-time': 'Startzeit',
  'seed-time-unit': 'Protokoll',
  'task-manage': 'Aufgaben verwalten',
  'max-concurrent-downloads': 'Maximal aktive Aufgaben',
  'max-connection-per-server': 'Maximale Verbindungen pro Server',
  'new-task-show-downloading': 'Nach hinzufügen einer Aufgabe zu aktiven Downloads wechseln',
  'no-confirm-before-delete-task': 'Vor dem Löschen der Aufgabe ist keine Bestätigung erforderlich',
  'continue': 'HTTPS/FTP Downloads fortsetzen wenn bereits angefangen',
  'task-completed-notify': 'Benachrichtigung nach abgeschlossenen Download anzeigen',
  'auto-purge-record': 'Download Protokoll beim Schließen der App löschen',
  'ui': 'UI',
  'appearance': 'Erscheinungsbild',
  'theme-auto': 'Automatisch',
  'theme-light': 'Hell',
  'theme-dark': 'Dunkel',
  'auto-hide-window': 'Fenster automatisch ausblenden',
  'run-mode': 'Rennen wie',
  'run-mode-standard': 'Standardanwendung',
  'run-mode-tray': 'Infobereichsanwendung',
  'run-mode-hide-tray': 'Infobereichsanwendung ausblenden',
  'tray-speedometer': 'Das Menüleistenfach zeigt die Echtzeitgeschwindigkeit an',
  'show-progress-bar': 'Fortschrittsbalken anzeigen',
  'language': 'Sprache',
  'change-language': 'Sprache ändern',
  'hide-app-menu': 'App Menü ausblenden (nur auf Windows & Linux)',
  'proxy': 'Proxy',
  'enable-proxy': 'Proxy aktivieren',
  'proxy-bypass-input-tips': 'Proxy-Einstellungen für diese Hosts und Domänen umgehen, eine pro Zeile',
  'proxy-scope-download': 'Herunterladen',
  'proxy-scope-update-app': 'Anwendung aktualisieren',
  'proxy-scope-update-trackers': 'Tracker aktualisieren',
  'proxy-tips': 'Proxy-Handbuch anzeigen',
  'bt-tracker': 'Tracker-Server',
  'bt-tracker-input-tips': 'Tracker-Server, einer pro Zeile',
  'bt-tracker-tips': 'Empfehlen:',
  'sync-tracker-tips': 'Synchronisieren',
  'auto-sync-tracker': 'Aktualisieren Sie die Trackerliste jeden Tag automatisch',
  'port': 'Listen Ports',
  'bt-port': 'BT Listen Port',
  'dht-port': 'DHT Listen Port',
  'security': 'Sicherheit',
  'rpc': 'RPC',
  'rpc-listen-port': 'RPC-Hörport',
  'rpc-secret': 'RPC-Geheimnis',
  'rpc-secret-tips': 'Geheime RPC-Anleitung anzeigen',
  'developer': 'Entwickler',
  'user-agent': 'User-Agent',
  'mock-user-agent': 'User-Agent simulieren',
  'aria2-conf-path': 'Integrierter aria2.conf-Pfad',
  'app-log-path': 'Appprotokollpfad',
  'download-session-path': 'Downloadsitzungspfad',
  'session-reset': 'Download-Session zurücksetzen',
  'session-reset-confirm': 'Sind Sie sicher, dass Sie die Download-Session zurücksetzen wollen?',
  'factory-reset': 'Werkseinstellungen',
  'factory-reset-confirm': 'Sollen die Einstellungen auf die Werkseinstellungen unwiderruflich zurückgesetzt werden?',
  'lab-warning': '⚠️ Die Aktivierung von experimentellen Funktionen kann zu App-Abstürzen oder Datenverlust führen!',
  'download-protocol': 'Protokoll',
  'protocols-default-client': 'Als Standardclient für die folgenden Protokolle festlegen',
  'protocols-magnet': 'Magnet [ magnet:// ]',
  'protocols-thunder': 'Thunder [ thunder:// ]',
  'browser-extensions': 'Erweiterungen',
  'baidu-exporter': 'Baidu Exporter',
  'browser-extensions-tips': 'Von der Community bereitgestellt, ',
  'baidu-exporter-help': 'mehr über die Verwendung zu erfahren',
  'auto-update': 'Auto-Update',
  'auto-check-update': 'Automatisch auf Updates prüfen',
  'last-check-update-time': 'letzte kontrolle update - zeit',
  'not-saved': 'Einstellungen nicht gespeichert',
  'not-saved-confirm': 'Die geänderten Einstellungen gehen verloren. Möchten Sie wirklich gehen?'
}
