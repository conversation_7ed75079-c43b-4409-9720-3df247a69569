{"name": "<PERSON><PERSON><PERSON>", "version": "1.8.19", "description": "A full-featured download manager", "homepage": "https://motrix.app", "author": {"name": "Dr_rOot", "email": "<EMAIL>"}, "copyright": "Copyright© Dr_rOot", "license": "MIT", "main": "./dist/electron/main.js", "repository": {"type": "git", "url": "**************:agalwood/Motrix.git"}, "scripts": {"release": "npm run build --publish onTagOrDraft", "build": "node .electron-vue/build.js && electron-builder", "build:applesilicon": "node .electron-vue/build.js && electron-builder --arm64 --mac", "build:github": "node .electron-vue/build.js", "build:dir": "node .electron-vue/build.js && electron-builder --dir", "build:clean": "cross-env BUILD_TARGET=clean node .electron-vue/build.js", "build:web": "cross-env BUILD_TARGET=web node .electron-vue/build.js", "dev": "node .electron-vue/dev-runner.js", "dev:renderer": "webpack serve --node-env development --hot --color --config .electron-vue/webpack.renderer.config.js --port 9080 --content-base app/dist", "lint": "eslint --ext .js,.vue -f ./node_modules/eslint-friendly-formatter src", "lint:fix": "eslint --ext .js,.vue -f ./node_modules/eslint-friendly-formatter --fix src", "pack": "npm run pack:main && npm run pack:renderer", "pack:main": "webpack --node-env production --progress --color --config .electron-vue/webpack.main.config.js", "pack:renderer": "webpack --node-env production --progress --color --config .electron-vue/webpack.renderer.config.js", "postinstall": "electron-builder install-app-deps && npm run lint:fix"}, "engines": {"node": ">=16.0.0"}, "dependencies": {"node-fetch": "^2.6.1", "ws": "^8.13.0"}, "devDependencies": {"@babel/core": "^7.21.8", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-transform-runtime": "^7.21.4", "@babel/preset-env": "^7.21.5", "@babel/register": "^7.21.0", "@babel/runtime": "^7.21.5", "@bany/curl-to-json": "^1.2.7", "@electron/notarize": "^1.2.3", "@electron/osx-sign": "^1.0.4", "@electron/remote": "^2.0.9", "@motrix/multispinner": "^0.2.4", "@motrix/nat-api": "^0.3.4", "@panter/vue-i18next": "^0.15.2", "@vue/eslint-config-standard": "^6.1.0", "ajv": "^8.12.0", "axios": "^1.4.0", "babel-eslint": "^10.1.0", "babel-loader": "^9.1.2", "babel-plugin-component": "^1.1.1", "bittorrent-peerid": "^1.3.6", "blob-util": "^2.0.2", "cfonts": "^3.2.0", "chalk": "^4.1.2", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "css-loader": "^6.7.3", "css-minimizer-webpack-plugin": "^5.0.0", "del": "^6.1.1", "electron": "^22.3.9", "electron-builder": "^24.4.0", "electron-devtools-installer": "^3.2.0", "electron-is": "^3.0.0", "electron-log": "^4.4.8", "electron-store": "^8.1.0", "electron-updater": "^6.1.0", "element-ui": "^2.15.13", "eslint": "^7.32.0", "eslint-friendly-formatter": "^4.0.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.12.0", "eslint-webpack-plugin": "^4.0.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.1", "i18next": "^22.4.15", "lodash": "^4.17.21", "mini-css-extract-plugin": "2.7.5", "node-loader": "^2.0.0", "normalize.css": "^8.0.1", "parse-torrent": "^9.1.5", "randomatic": "^3.1.1", "sass": "1.62.1", "sass-loader": "^12.6.0", "style-loader": "^3.3.2", "terser-webpack-plugin": "^5.3.8", "vue": "^2.7.14", "vue-electron": "^1.0.6", "vue-loader": "^15.10.1", "vue-router": "^3.6.5", "vue-selectable": "^0.5.0", "vue-style-loader": "^4.1.3", "vue-template-compiler": "^2.7.14", "vuex": "^3.6.2", "vuex-router-sync": "^5.0.0", "webpack": "^5.82.1", "webpack-cli": "^5.1.1", "webpack-dev-server": "^4.15.0", "webpack-hot-middleware": "^2.25.3", "webpack-merge": "^5.8.0", "worker-loader": "^3.0.8"}}