const userKeys = [
  'auto-check-update',
  'auto-hide-window',
  'auto-sync-tracker',
  'cookie',
  'enable-upnp',
  'engine-bin-path',
  'engine-max-connection-per-server',
  'favorite-directories',
  'hide-app-menu',
  'history-directories',
  'keep-seeding',
  'keep-window-state',
  'last-check-update-time',
  'last-sync-tracker-time',
  'locale',
  'log-level',
  'new-task-show-downloading',
  'no-confirm-before-delete-task',
  'open-at-login',
  'protocols',
  'proxy',
  'resume-all-when-app-launched',
  'run-mode',
  'show-progress-bar',
  'task-notification',
  'theme',
  'tracker-source',
  'tray-speedometer'
]

const systemKeys = [
  'all-proxy-passwd',
  'all-proxy-user',
  'all-proxy',
  'allow-overwrite',
  'allow-piece-length-change',
  'always-resume',
  'async-dns',
  'auto-file-renaming',
  'bt-enable-hook-after-hash-check',
  'bt-enable-lpd',
  'bt-exclude-tracker',
  'bt-external-ip',
  'bt-force-encryption',
  'bt-hash-check-seed',
  'bt-load-saved-metadata',
  'bt-max-peers',
  'bt-metadata-only',
  'bt-min-crypto-level',
  'bt-prioritize-piece',
  'bt-remove-unselected-file',
  'bt-request-peer-speed-limit',
  'bt-require-crypto',
  'bt-save-metadata',
  'bt-seed-unverified',
  'bt-stop-timeout',
  'bt-tracker-connect-timeout',
  'bt-tracker-interval',
  'bt-tracker-timeout',
  'bt-tracker',
  'check-integrity',
  'checksum',
  'conditional-get',
  'connect-timeout',
  'content-disposition-default-utf8',
  'continue',
  'dht-file-path',
  'dht-file-path6',
  'dht-listen-port',
  'dir',
  'dry-run',
  'enable-http-keep-alive',
  'enable-http-pipelining',
  'enable-mmap',
  'enable-peer-exchange',
  'file-allocation',
  'follow-metalink',
  'follow-torrent',
  'force-save',
  'force-sequential',
  'ftp-passwd',
  'ftp-pasv',
  'ftp-proxy-passwd',
  'ftp-proxy-user',
  'ftp-proxy',
  'ftp-reuse-connection',
  'ftp-type',
  'ftp-user',
  'gid',
  'hash-check-only',
  'header',
  'http-accept-gzip',
  'http-auth-challenge',
  'http-no-cache',
  'http-passwd',
  'http-proxy-passwd',
  'http-proxy-user',
  'http-proxy',
  'http-user',
  'https-proxy-passwd',
  'https-proxy-user',
  'https-proxy',
  'index-out',
  'listen-port',
  'lowest-speed-limit',
  'max-concurrent-downloads',
  'max-connection-per-server',
  'max-download-limit',
  'max-file-not-found',
  'max-mmap-limit',
  'max-overall-download-limit',
  'max-overall-upload-limit',
  'max-resume-failure-tries',
  'max-tries',
  'max-upload-limit',
  'metalink-base-uri',
  'metalink-enable-unique-protocol',
  'metalink-language',
  'metalink-location',
  'metalink-os',
  'metalink-preferred-protocol',
  'metalink-version',
  'min-split-size',
  'no-file-allocation-limit',
  'no-netrc',
  'no-proxy',
  'no-want-digest-header',
  'out',
  'parameterized-uri',
  'pause-metadata',
  'pause',
  'piece-length',
  'proxy-method',
  'realtime-chunk-checksum',
  'referer',
  'remote-time',
  'remove-control-file',
  'retry-wait',
  'reuse-uri',
  'rpc-listen-port',
  'rpc-save-upload-metadata',
  'rpc-secret',
  'seed-ratio',
  'seed-time',
  'select-file',
  'split',
  'ssh-host-key-md',
  'stream-piece-selector',
  'timeout',
  'uri-selector',
  'use-head',
  'user-agent'
]

const needRestartKeys = [
  'dht-listen-port',
  'hide-app-menu',
  'listen-port',
  'rpc-listen-port',
  'rpc-secret'
]

export {
  userKeys,
  systemKeys,
  needRestartKeys
}
