{"comments": false, "env": {"main": {"presets": ["@babel/preset-env"]}, "renderer": {"presets": ["@babel/preset-env"], "plugins": [["component", {"libraryName": "element-ui", "styleLibraryName": "theme-chalk"}]]}, "web": {"presets": ["@babel/preset-env"], "plugins": [["component", {"libraryName": "element-ui", "styleLibraryName": "theme-chalk"}]]}}, "plugins": ["@babel/plugin-proposal-class-properties", "@babel/plugin-transform-runtime"]}