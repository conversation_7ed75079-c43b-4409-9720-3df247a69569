export default {
  'basic': '<PERSON>ás<PERSON>',
  'advanced': 'Avançado',
  'lab': 'Lab',
  'save': '<PERSON><PERSON> & Aplicar',
  'save-success-message': 'Salvar preferências com sucesso',
  'save-fail-message': '<PERSON>var preferências falhadas',
  'discard': 'Descartar',
  'startup': 'Inicialização',
  'open-at-login': 'Abra no login',
  'keep-window-state': 'Restaurar tamanho e posição da janela',
  'auto-resume-all': 'Auto resumir todas as tarefas não finalizadas',
  'default-dir': 'Diretório Padrão',
  'mas-default-dir-tips': 'Devido às restrições de permissões do sandbox da App Store, recomenda-se que o diretório de download padrão seja definido como o diretório de downloads',
  'transfer-settings': 'Transmissão',
  'transfer-speed-upload': 'limite de envio',
  'transfer-speed-download': 'limite de transferência',
  'transfer-speed-unlimited': 'Ilimitado',
  'bt-settings': 'BitTorrent',
  'bt-save-metadata': 'Salvar link magnético como arquivo torrent',
  'bt-auto-download-content': 'Baixar automaticamente ímã e conteúdo de torrent',
  'bt-force-encryption': 'Forçar criptografia BT',
  'keep-seeding': 'Continue semeando até pará-lo manualmente',
  'seed-ratio': 'Proporção de sementes',
  'seed-time': 'Hora da Semente',
  'seed-time-unit': 'minutos',
  'task-manage': 'Gerenciador de Tarefas',
  'max-concurrent-downloads': 'Máximo de tarefas ativas',
  'max-connection-per-server': 'Máximo de coneções por servidor',
  'new-task-show-downloading': 'Auto exibir progresso depois de adicionar uma tarefa',
  'no-confirm-before-delete-task': 'Nenhuma confirmação é necessária antes de excluir a tarefa',
  'continue': 'Continuar',
  'task-completed-notify': 'Notificação após o download ser completado',
  'auto-purge-record': 'Auto remover registro de download quando o app for finalizado',
  'ui': 'UI',
  'appearance': 'Aparência',
  'theme-auto': 'Automático',
  'theme-light': 'Clara',
  'theme-dark': 'Escura',
  'auto-auto-hide-window': 'Ocultar janelas automaticamente',
  'run-mode': 'Correr como',
  'run-mode-standard': 'Aplicação padrão',
  'run-mode-tray': 'Aplicativo de bandeja',
  'run-mode-hide-tray': 'Ocultar aplicativo da bandeja',
  'tray-speedometer': 'A bandeja da barra de menus mostra a velocidade em tempo real',
  'show-progress-bar': 'Mostrar barra de progresso de download',
  'language': 'Idioma',
  'change-language': 'Mudar o Idioma',
  'hide-app-menu': 'Ocultar o Menu do App (Windows & Linux apenas)',
  'proxy': 'Proxy',
  'enable-proxy': 'Habilitar Proxy',
  'proxy-bypass-input-tips': 'Ignorar configurações de proxy para esses hosts e domínios, um por linha',
  'proxy-scope-download': 'Download',
  'proxy-scope-update-app': 'Atualizar Aplicativo',
  'proxy-scope-update-trackers': 'Atualizar rastreadores',
  'proxy-tips': 'Exibir manual do proxy',
  'bt-tracker': 'Rastreadores',
  'bt-tracker-input-tips': 'servidor rastreador, um por linha',
  'bt-tracker-tips': 'Recomendar:',
  'sync-tracker-tips': 'Sincronizar',
  'auto-sync-tracker': 'Atualize a lista de rastreadores todos os dias automaticamente',
  'port': 'Portas de escuta',
  'bt-port': 'Porta de escuta BT',
  'dht-port': 'Porta de escuta DHT',
  'security': 'Segurança',
  'rpc': 'RPC',
  'rpc-listen-port': 'Porta de Escuta RPC',
  'rpc-secret': 'Segredo de RPC',
  'rpc-secret-tips': 'Veja o manual secreto de RPC',
  'developer': 'Desenvolverdor',
  'user-agent': 'User-Agent',
  'mock-user-agent': 'Mock User-Agent',
  'aria2-conf-path': 'Caminho de aria2.conf incorporado',
  'app-log-path': 'Diretório de logs',
  'download-session-path': 'Diretório da sessão de Downloads',
  'factory-reset': 'Configurações de Fábrica',
  'factory-reset-confirm': 'Você tem certeza de que deseja resetar às configurações de fábrica?',
  'lab-warning': '⚠️ Habilitar os recursos de lab pode causar perda de dados e fechar o app inesperadamete. Use por sua conta e risco!',
  'download-protocol': 'Protocolo',
  'protocols-default-client': 'Definir como cliente padrão para os seguintes protocolos',
  'protocols-magnet': 'Magnético [ magnet:// ]',
  'protocols-thunder': 'Trovão [ thunder:// ]',
  'browser-extensions': 'Extensões',
  'baidu-exporter': 'BaiduExporter',
  'browser-extensions-tips': 'Fornecido pela comunidade, ',
  'baidu-exporter-help': 'Clique aqui ver as instruções de uso',
  'auto-update': 'Atualização automática',
  'auto-check-update': 'A verificação automática de atualizações',
  'last-check-update-time': 'última verificação do tempo de atualização',
  'not-saved': 'Preferências não salvas',
  'not-saved-confirm': 'As preferências modificadas serão perdidas. Tem certeza de que deseja sair?'
}
