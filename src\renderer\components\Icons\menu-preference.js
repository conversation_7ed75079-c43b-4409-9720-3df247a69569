import Icon from '@/components/Icons/Icon'

Icon.register({
  'menu-preference': {
    'width': 24,
    'height': 24,
    'raw': `<line fill="none" stroke-miterlimit="10" x1="14" y1="4" x2="23" y2="4"/> <line fill="none" stroke-miterlimit="10" x1="1" y1="4" x2="4" y2="4"/> <line data-color="color-2" fill="none" stroke-miterlimit="10" x1="22" y1="12" x2="23" y2="12"/> <line data-color="color-2" fill="none" stroke-miterlimit="10" x1="1" y1="12" x2="12" y2="12"/> <line fill="none" stroke-miterlimit="10" x1="14" y1="20" x2="23" y2="20"/> <line fill="none" stroke-miterlimit="10" x1="1" y1="20" x2="4" y2="20"/> <circle fill="none" stroke-miterlimit="10" cx="7" cy="4" r="3"/> <circle data-color="color-2" fill="none" stroke-miterlimit="10" cx="15" cy="12" r="3"/> <circle fill="none" stroke-miterlimit="10" cx="7" cy="20" r="3"/>`,
    'g': {
      'stroke': 'currentColor',
      'stroke-linecap': 'round',
      'stroke-linejoin': 'round',
      'stroke-width': '2'
    }
  }
})
