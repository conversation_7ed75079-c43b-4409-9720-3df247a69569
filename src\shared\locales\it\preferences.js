export default {
  'basic': 'Base',
  'advanced': 'Avanzate',
  'lab': 'Sperimentali',
  'save': 'Salva e applica',
  'save-success-message': 'Preferenze salvate con successo',
  'save-fail-message': 'Preferenze non salvate',
  'discard': 'Scarta modifiche',
  'startup': 'Avvio',
  'open-at-login': 'Apri al login',
  'keep-window-state': 'Mantieni le dimensioni e la posizione della finestra quando l\'app viene chiusa',
  'auto-resume-all': 'Ricomincia tutti le attività non finite alla riapertura dell\'app',
  'default-dir': 'Posizione di default per i download',
  'mas-default-dir-tips': 'A causa delle restrizioni imposte dall\'App Store, è raccomandato impostare la directory di default su ~/Downloads',
  'transfer-settings': 'Transmissione dati',
  'transfer-speed-upload': 'Limite di uplooad',
  'transfer-speed-download': 'Limite di download',
  'transfer-speed-unlimited': 'Illimitata',
  'bt-settings': 'BitTorrent',
  'bt-save-metadata': 'Salva magnet link come file torrent',
  'bt-auto-download-content': 'Scarica automaticamente il contenuto di magnete e torrent',
  'bt-force-encryption': 'Forzare la crittografia di BT',
  'keep-seeding': 'Continua a seminare fino a interromperlo manualmente',
  'seed-ratio': 'Rapporto di semina',
  'seed-time': 'Tempo di semi',
  'seed-time-unit': 'minuti',
  'task-manage': 'Gestione attività',
  'max-concurrent-downloads': 'Massimo numero di attività eseguibili contemporaneamente',
  'max-connection-per-server': 'Massimo numero di connessioni simultanee per server',
  'new-task-show-downloading': 'Mostra automaticamente il download quando aggiungo una nuova attività',
  'no-confirm-before-delete-task': 'Nessuna conferma richiesta prima di eliminare un\'attività',
  'continue': 'Continua',
  'task-completed-notify': 'Notifica quando un download è finito',
  'auto-purge-record': 'Elimina automaticamente la cronologia di download quando esco l\'app viene chiusa',
  'ui': 'UI',
  'appearance': 'Aspetto',
  'theme-auto': 'Auto',
  'theme-light': 'Chiaro',
  'theme-dark': 'Scuro',
  'auto-hide-window': 'Nascondi automaticamente la finestra',
  'run-mode': 'Avvia come',
  'run-mode-standard': 'Applicazione Standard ',
  'run-mode-tray': 'Applicazione della barra delle applicazioni',
  'run-mode-hide-tray': 'Nascondi applicazione della barra delle applicazioni',
  'tray-speedometer': 'La barra dei menu mostra la velocità in tempo reale',
  'show-progress-bar': 'Mostra la barra di progresso del download',
  'language': 'Lingua',
  'change-language': 'Cambia lingua',
  'hide-app-menu': 'Nascondi dal menu delle app (Solo Windows & Linux)',
  'proxy': 'Proxy',
  'enable-proxy': 'Usa Proxy',
  'proxy-bypass-input-tips': 'Non usare proxy per questi Host e Domini, uno per linea',
  'proxy-scope-download': 'Scarica',
  'proxy-scope-update-app': 'Aggiorna applicazione',
  'proxy-scope-update-trackers': 'Aggiorna tracker',
  'proxy-tips': 'Guida all\'uso dei proxy (In Inglese)',
  'bt-tracker': 'Server di monitoraggio',
  'bt-tracker-input-tips': 'Tracker servers, uno per linea',
  'bt-tracker-tips': 'Raccomandati: ',
  'sync-tracker-tips': 'Sincronizza',
  'auto-sync-tracker': 'Aggiorna automaticamente la lista dei tracker ogni giorno',
  'port': 'Porte in ascolto',
  'bt-port': 'Porte in ascolto BT',
  'dht-port': 'Porte in ascolto DHT',
  'security': 'Sicurezza',
  'rpc': 'RPC',
  'rpc-listen-port': 'Porta di Ascolto RPC',
  'rpc-secret': 'RPC Secret',
  'rpc-secret-tips': 'Guida sull\'uso degli rpc secret (in Inglese)',
  'developer': 'Sviluppatore',
  'user-agent': 'User-Agent',
  'mock-user-agent': 'Cambia User-Agent',
  'aria2-conf-path': 'Percorso incorporato di aria2.conf',
  'app-log-path': 'Posizione log dell\'app',
  'download-session-path': 'Posizione sessione di download',
  'factory-reset': 'Reset di fabbrica',
  'factory-reset-confirm': 'Sei sicuro di voler riportare alle impostazioni di fabbrica l\'app?',
  'lab-warning': '⚠️ Ablilitare le funzioni sperimentali potrebbe risultare in un crash o una perdita di dati, decidi a tuo rischio e pericolo!',
  'download-protocol': 'Protocolli',
  'protocols-default-client': 'Imposta di Default i seguenti protocolli',
  'protocols-magnet': 'Magnet [ magnet:// ]',
  'protocols-thunder': 'Thunder [ thunder:// ]',
  'browser-extensions': 'Estensione per browser',
  'baidu-exporter': 'BaiduExporter',
  'browser-extensions-tips': 'Fornita dalla community, ',
  'baidu-exporter-help': 'Clicca qui per scoprire il funzionamento',
  'auto-update': 'Auto Update',
  'auto-check-update': 'Verifica automaticamente la disponibilità di aggiornamenti',
  'last-check-update-time': 'Ultima volta quando gli aggiornamenti sono stati verificati',
  'not-saved': 'Preferenze non salvate',
  'not-saved-confirm': 'Le preferenze modificate andranno perse, sei sicuro di uscire?'
}
